import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  Button,
  Avatar,
  Divider,
  Tabs,
  Tab,
  CircularProgress,
  useMediaQuery,
  useTheme,
  Switch,
  FormControlLabel,
  Alert
} from '@mui/material';
import {
  AccountCircle,
  Logout,
  Add,
  CreditCard,
  Settings,
  Notifications
} from '@mui/icons-material';
import BillingHistory from './BillingHistory';
import UsageHistory from './UsageHistory';
import {
  isNotificationSupported,
  getNotificationPermission,
  requestNotificationPermission
} from '../utils/notifications';

interface AccountScreenProps {
  userEmail: string;
  firstName?: string;
  profilePictureUrl?: string;
  userCredits: number;
  creditsLoading: boolean;
  onLogout: () => void;
  token: string;
  notificationsEnabled?: boolean;
  onNotificationsChange?: (enabled: boolean) => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`account-tabpanel-${index}`}
      aria-labelledby={`account-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `account-tab-${index}`,
    'aria-controls': `account-tabpanel-${index}`,
  };
}

const AccountScreen: React.FC<AccountScreenProps> = ({
  userEmail,
  firstName,
  profilePictureUrl,
  userCredits,
  creditsLoading,
  onLogout,
  token,
  notificationsEnabled = true,
  onNotificationsChange
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [notificationPermission, setNotificationPermission] = useState(getNotificationPermission());
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Get initials from email for avatar
  const getInitials = (email: string) => {
    const name = email.split('@')[0];
    return name.charAt(0).toUpperCase();
  };

  const handleAddCredits = () => {
    navigate('/add-credits');
  };

  const handleNotificationToggle = async (enabled: boolean) => {
    if (enabled && notificationPermission !== 'granted') {
      const permission = await requestNotificationPermission();
      setNotificationPermission(permission);

      if (permission !== 'granted') {
        // Don't enable notifications if permission was denied
        return;
      }
    }

    if (onNotificationsChange) {
      onNotificationsChange(enabled);
    }
  };



  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: { xs: 0, sm: 3 } }}>
      {/* Header */}
      <Box sx={{ mb: 4, px: { xs: 2, sm: 0 } }}>
        <Typography variant="h4" sx={{ fontWeight: 400, mb: 1, fontSize: { xs: '1.75rem', sm: '2.125rem' } }}>
          Account
        </Typography>
        <Typography variant="body1" sx={{ color: 'text.secondary' }}>
          Manage your profile, billing, and usage history
        </Typography>
      </Box>

      {/* Overview Section */}
      <Paper
        elevation={0}
        sx={{
          p: { xs: 2, sm: 3, md: 4 },
          mb: 3,
          mx: { xs: 2, sm: 0 },
          border: 1,
          borderColor: 'divider',
          borderRadius: 1
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, flexDirection: { xs: 'column', sm: 'row' }, textAlign: { xs: 'center', sm: 'left' } }}>
          <Avatar
            src={profilePictureUrl}
            sx={{
              width: { xs: 56, sm: 64 },
              height: { xs: 56, sm: 64 },
              mr: { xs: 0, sm: 3 },
              mb: { xs: 2, sm: 0 },
              bgcolor: 'primary.main',
              fontSize: '1.5rem'
            }}
          >
            {!profilePictureUrl && getInitials(userEmail)}
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 500, mb: 0.5 }}>
              {firstName || userEmail.split('@')[0]}
            </Typography>
            <Typography variant="body2" sx={{ color: 'text.secondary', wordBreak: 'break-word' }}>
              {userEmail}
            </Typography>
          </Box>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3, flexDirection: { xs: 'column', sm: 'row' }, gap: { xs: 2, sm: 0 } }}>
          <Box sx={{ textAlign: { xs: 'center', sm: 'left' } }}>
            <Typography variant="h6" sx={{ fontWeight: 500, mb: 0.5 }}>
              Current Credit Balance
            </Typography>
            <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main', fontSize: { xs: '1.75rem', sm: '2.125rem' } }}>
              {creditsLoading ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: { xs: 'center', sm: 'flex-start' } }}>
                  <CircularProgress size={24} />
                  <span style={{ fontSize: '1rem' }}>Loading...</span>
                </Box>
              ) : (
                `${userCredits} Credits`
              )}
            </Typography>
          </Box>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleAddCredits}
            size="large"
            sx={{
              borderRadius: 1,
              textTransform: 'none',
              fontWeight: 500,
              px: 4,
              py: 1.5,
              width: { xs: '100%', sm: 'auto' }
            }}
          >
            Add More Credits
          </Button>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Button
          onClick={onLogout}
          startIcon={<Logout />}
          variant="outlined"
          sx={{
            color: 'error.main',
            borderColor: 'error.main',
            textTransform: 'none',
            fontWeight: 500,
            px: 3,
            py: 1,
            '&:hover': {
              backgroundColor: 'error.main',
              color: 'error.contrastText',
              borderColor: 'error.main'
            },
            borderRadius: 1
          }}
        >
          Sign Out
        </Button>
      </Paper>

      {/* Tabs Section */}
      <Paper
        elevation={0}
        sx={{
          mx: { xs: 2, sm: 0 },
          border: 1,
          borderColor: 'divider',
          borderRadius: 1
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="account tabs"
            sx={{ px: { xs: 2, sm: 3 } }}
          >
            <Tab
              label={isMobile ? undefined : "Billing History"}
              icon={<CreditCard />}
              iconPosition={isMobile ? undefined : "start"}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                minWidth: isMobile ? 'auto' : undefined,
                px: isMobile ? 1 : undefined
              }}
              {...a11yProps(0)}
            />
            <Tab
              label={isMobile ? undefined : "Usage History"}
              icon={<AccountCircle />}
              iconPosition={isMobile ? undefined : "start"}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                minWidth: isMobile ? 'auto' : undefined,
                px: isMobile ? 1 : undefined
              }}
              {...a11yProps(1)}
            />
            <Tab
              label={isMobile ? undefined : "Settings"}
              icon={<Settings />}
              iconPosition={isMobile ? undefined : "start"}
              sx={{
                textTransform: 'none',
                fontWeight: 500,
                minWidth: isMobile ? 'auto' : undefined,
                px: isMobile ? 1 : undefined
              }}
              {...a11yProps(2)}
            />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <BillingHistory token={token} />
        </TabPanel>
        <TabPanel value={tabValue} index={1}>
          <UsageHistory token={token} />
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ p: { xs: 2, sm: 3 } }}>
            <Typography variant="h6" sx={{ mb: 3, fontWeight: 500 }}>
              Browser Notification Settings
            </Typography>

            {!isNotificationSupported() && (
              <Alert severity="warning" sx={{ mb: 3 }}>
                Browser notifications are not supported in your current browser.
              </Alert>
            )}

            <FormControlLabel
              control={
                <Switch
                  checked={notificationsEnabled && notificationPermission === 'granted'}
                  onChange={(e) => handleNotificationToggle(e.target.checked)}
                  disabled={!isNotificationSupported()}
                />
              }
              label={
                <Box>
                  <Typography variant="body1" sx={{ fontWeight: 500 }}>
                    Browser Notifications
                  </Typography>
                  <Typography variant="body2" sx={{ color: 'text.secondary', mt: 0.5 }}>
                    Get browser notifications when compression jobs complete
                  </Typography>
                </Box>
              }
              sx={{ alignItems: 'flex-start', mb: 2 }}
            />

            {notificationPermission === 'denied' && (
              <Alert severity="info" sx={{ mt: 2 }}>
                Browser notifications are blocked. To enable them, please allow notifications for this site in your browser settings.
              </Alert>
            )}
          </Box>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default AccountScreen;
